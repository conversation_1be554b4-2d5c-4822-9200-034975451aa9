import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@radix-ui/react-avatar";
import { IconEdit, IconUser, IconUsersGroup } from "@tabler/icons-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { calculateAge, FormatS3ImgUrl, generateNickName } from "@/utils/common";

export default function LeftPanel({ data }) {
  return (
    <div className="flex flex-col gap-[16px] mb-5">
      <div className="bg-sidebar p-4 rounded-2xl">
        <Avatar className="w-[54px] h-[54px] mb-[12px] rounded-full bg-[#999] text-white flex items-center justify-center">
          {data?.avatar ? (
            <AvatarImage src={FormatS3ImgUrl(data?.avatar)} alt="Avatar" className="object-cover rounded-full w-full h-full" />
          ) : (
            <AvatarFallback className="text-lg">
              {generateNickName(data?.username)}
            </AvatarFallback>
          )}
        </Avatar>
        <div className="text-lg font-medium">
          {data?.username} ({calculateAge(data?.dob)})
        </div>
        <div className="text-sm">{data?.city} - VTL: 0</div>
        <div className="text-sm">Timezone: GMT+5:30</div>
        <hr className="my-3"></hr>
        <div className="flex flex-wrap">
          <div className="basis-1/2">
            <div className="text-sm">Relation Status</div>
            <div className="text-sm font-semibold">
              {data?.relationshipStatus?.title}
            </div>
          </div>
          <div className="basis-1/2">
            <div className="text-sm">Location</div>
            <div className="text-sm font-semibold">{data?.country?.name}</div>
          </div>
          <div className="basis-2/2 mt-3">
            <div className="text-sm">Interests</div>
            <div className="text-sm font-semibold">
              {data.interest?.length > 0
                ? data?.interest?.map((item: any) => item.title).join(", ")
                : "No Interests"}
            </div>
          </div>
        </div>
      </div>

      <div>
        <div className="text-sm font-semibold mb-[10px]">Affiliate</div>
        <div className="">
          <Select>
            <SelectTrigger className="w-full !h-12 rounded-xl border border-gray-300 bg-white px-4 text-sm focus:ring-0 focus:outline-none">
              <SelectValue placeholder="Select option" />
            </SelectTrigger>
            <SelectContent className="rounded-xl">
              <SelectItem value="yes">Yes</SelectItem>
              <SelectItem value="no">No</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="bg-sidebar p-4 rounded-2xl">
        <div className="text-base font-semibold mb-[12px]">Notes</div>
        <div className="flex mb-4 overflow-x-auto space-x-3 flex-nowrap">
          <div className="bg-sidebar-accent p-3 rounded-lg min-w-[200px]">
            <div className="text-sm">He is back after 3 days.</div>
            <hr className="my-3"></hr>
            <div className="flex gap-4 items-center justify-between">
              <div className="text-xs">Edited : Dec 2, 2024</div>
              <div></div>
            </div>
          </div>
        </div>
        <Input
          type="text"
          placeholder="Enter Note"
          className="text-sm h-[42px] shadow-none"
        />
      </div>

      <div className="flex flex-col gap-3 bg-sidebar p-4 rounded-2xl">
        <div className="bg-sidebar-accent p-3 rounded-lg">
          <div className="flex gap-4 items-center justify-between">
            <div className="flex items-center gap-1 text-sm">
              <IconUser width="18px" /> Name
            </div>
            <button className="rounded-full flex justify-center w-[24px] h-[24px] bg-background">
              <IconEdit width="14px" />
            </button>
          </div>
          <hr className="my-3"></hr>
          <ul className="list-disc ps-5">
            <li className="text-xs">{data?.username}</li>
          </ul>
        </div>
        <div className="bg-sidebar-accent p-3 rounded-lg">
          <div className="flex gap-4 items-center justify-between">
            <div className="flex items-center gap-1 text-sm">
              <IconUser width="18px" /> Living Conditions
            </div>
            <button className="rounded-full flex justify-center w-[24px] h-[24px] bg-background">
              <IconEdit width="14px" />
            </button>
          </div>
          <hr className="my-3"></hr>
          <ul className="list-disc ps-5">
            <li className="text-xs">Lives in {data?.city}</li>
            <li className="text-xs">Splits bills with partner</li>
          </ul>
        </div>
        <div className="bg-sidebar-accent p-3 rounded-lg">
          <div className="flex gap-4 items-center justify-between">
            <div className="flex items-center gap-1 text-sm">
              <IconUsersGroup width="18px" /> Family / Pets
            </div>
            <button className="rounded-full flex justify-center w-[24px] h-[24px] bg-background">
              <IconEdit width="14px" />
            </button>
          </div>
          <hr className="my-3"></hr>
          <ul className="list-disc ps-5">
            <li className="text-xs">In a complicated long-term relationship</li>
            <li className="text-xs">
              {data?.kids !== "0" ? `Kids` : "No Kids"}
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
