import { create } from "zustand";
import io from "socket.io-client";
import { useAuthStore, getAccessToken } from "./authStore";

interface User {
  id: number;
  name: string | null;
  username: string;
  email: string | null;
  city: string;
  avatar: string | null;
  isOnline: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ChatUser {
  id: number;
  name: string;
  location: string;
  avatar?: string;
}

interface ApiMessage {
  id: number;
  sender: User;
  senderId: number;
  conversationId: number;
  message: string;
  messageType: "text" | "image" | "file";
  messageStatus: "sent" | "delivered" | "read";
  isRead: boolean;
  metadata: any;
  createdAt: string;
  updatedAt: string;
}
interface ModeratorMessage {
  id: number;
  senderId: number;
  replyToId: number | null;
  moderatorId: number | null;
  conversationId: number;
  message: string;
  messageType: "text" | "image" | "file";
  messageStatus: "sent" | "delivered" | "read";
  isRead: boolean;
  metadata: any;
  createdAt: string;
  updatedAt: string;
}

interface Message {
  id: number;
  senderId: number;
  receiverId: number;
  message: string;
  messageType: "text" | "image" | "file";
  timestamp: string;
  status: "sent" | "delivered" | "read";
  conversationId: number;
  sender?: "me" | "other";
}

interface ChatState {
  activeChatUser: ChatUser | null;
  messages: Record<number, Message[]>;
  socket: any;
  conversations: any[];
  isConnected: boolean;
  currentUserId: number | null;
  setConversations: (conversations: any[]) => void;
  moderatorMessages: Record<number, ModeratorMessage[]>;
  moderatorMessagesMeta: Record<
    number,
    {
      total: number;
      page: number;
      limit: number;
      pages: number;
      search: string | null;
    }
  >;
  isLoadingMoreMessages: boolean;

  initializeSocket: () => () => void;
  disconnectSocket: () => void;
  setMessages: (userId: number, messages: Message[]) => void;

  "mod-sendMessage": (conversationId: number, message: string) => void;
  "mod-setMessages": (
    conversationId: number,
    messages: ModeratorMessage[],
    meta: any,
    isLoadingMore?: boolean
  ) => void;
  "mod-addMessage": (conversationId: number, message: ModeratorMessage) => void;

  transformApiMessage: (
    apiMessage: ApiMessage,
    currentUserId: number
  ) => Message;
  processApiMessages: (
    apiMessages: ApiMessage[],
    currentUserId: number
  ) => Message[];
  addRoomJoined: () => boolean;
}

const useChatStore = create<ChatState>((set, get) => ({
  activeChatUser: null,
  messages: {},
  conversations: [],
  socket: null,
  isConnected: false,
  currentUserId: null,
  moderatorMessages: {},
  moderatorMessagesMeta: {},
  isLoadingMoreMessages: false,
  transformApiMessage: (
    apiMessage: ApiMessage,
    currentUserId: number
  ): Message => {
    return {
      id: apiMessage.id,
      senderId: apiMessage.senderId,
      receiverId:
        currentUserId === apiMessage.senderId
          ? apiMessage.conversationId
            ? 0
            : currentUserId
          : currentUserId,
      message: apiMessage.message,
      messageType: apiMessage.messageType,
      timestamp: apiMessage.createdAt,
      status: apiMessage.messageStatus,
      conversationId: apiMessage.conversationId,
      sender: apiMessage.senderId === currentUserId ? "me" : "other",
    };
  },

  processApiMessages: (
    apiMessages: ApiMessage[],
    currentUserId: number
  ): Message[] => {
    return apiMessages.map((apiMessage) =>
      get().transformApiMessage(apiMessage, currentUserId)
    );
  },

  initializeSocket: () => {
    const token = getAccessToken();
    const authUser: any = useAuthStore.getState().auth.user;
    const userId = authUser?.id ? parseInt(authUser.id, 10) : null;

    if (!token || !userId) {
      console.error("Cannot initialize socket: missing token or userId", {
        token: !!token,
        userId,
      });
      return () => {};
    }

    set({ currentUserId: userId });
    const socketUrl = import.meta.env.VITE_SOCKET_URL;

    if (!socketUrl) {
      console.error("Socket URL not configured in environment variables");
      return () => {};
    }

    const socket = io(socketUrl, {
      auth: {
        token: "customer_token",
        userId: parseInt(userId.toString()),
      },
      transports: ["websocket", "polling"],
      timeout: 20000,
    });

    socket.on("connect", () => {
      console.log("Socket connected successfully");
      set({ isConnected: true, socket });
    });

    socket.on("disconnect", () => {
      console.log("Socket disconnected:");
      set({ isConnected: false });
    });

    socket.on("direct_message_received", (response) => {
      get()["mod-addMessage"](
        response?.message?.conversationId,
        response?.message
      );
    });

    socket.on("connect_error", (error) => {
      console.error("Socket connection error:", error);
      set({ isConnected: false });
    });

    socket.on("reconnect", () => {
      set({ isConnected: true });
    });

    socket.on("reconnect_error", (error) => {
      console.error("Socket reconnection error:", error);
    });

    socket.on("moderator_message_event", (response) => {
      console.log("moderator_message_event", response);
      useChatStore.getState().setConversations(response.session);
    });

    socket.emit("join_domain_room", { domainName: "xyz.website.com" });

    return () => {
      socket.disconnect();
      set({ socket: null, isConnected: false });
    };
  },

  disconnectSocket: () => {
    const { socket } = get();
    if (socket) {
      socket.disconnect();
      set({ socket: null, isConnected: false });
    }
  },

  setMessages: (userId, messages) => {
    set((state) => ({
      messages: {
        ...state.messages,
        [userId]: messages,
      },
    }));
  },

  setConversations: (conversations) => {
    console.log("settled");
    set({ conversations });
  },

  addRoomJoined: () => {
    const { socket } = get();
    socket.emit("join_domain_room", { domainName: "xyz.website.com" });
    return true;
  },

  "mod-sendMessage": ({
    message,
    receiverId,
    senderId,
    messageType = "text",
    conversationId,
  }: any) => {
    const { socket, isConnected, currentUserId } = get();

    if (!socket || !isConnected || !currentUserId) {
      console.error(
        "Cannot send moderator message: Socket not connected or user not authenticated"
      );
      return;
    }

    try {
      const messageData = {
        receiverId,
        senderId,
        message,
        messageType,
      };

      socket.emit("send_chat_message", messageData);

      const newMessage = {
        senderId,
        replyToId: null,
        moderatorId: currentUserId,
        conversationId,
        message,
        messageType: "text",
        messageStatus: "sent",
        isRead: false,
        metadata: null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      get()["mod-addMessage"](conversationId, newMessage);
    } catch (error) {
      console.error("Error sending moderator message:", error);
    }
  },

  "mod-setMessages": (
    conversationId: number,
    messages: ModeratorMessage[],
    meta: any,
    isLoadingMore = false
  ) => {
    set((state) => {
      const existingMessages = state.moderatorMessages[conversationId] || [];
      const newMessages = isLoadingMore
        ? [...messages, ...existingMessages]
        : messages;

      return {
        moderatorMessages: {
          ...state.moderatorMessages,
          [conversationId]: newMessages,
        },
        moderatorMessagesMeta: {
          ...state.moderatorMessagesMeta,
          [conversationId]: meta,
        },
        isLoadingMoreMessages: false,
      };
    });
  },

  "mod-addMessage": (conversationId: number, message: ModeratorMessage) => {
    set((state) => ({
      moderatorMessages: {
        ...state.moderatorMessages,
        [conversationId]: [
          ...(state.moderatorMessages[conversationId] || []),
          message,
        ],
      },
    }));
  },
}));

export const cleanupChatSocket = () => {
  const { disconnectSocket } = useChatStore.getState();
  disconnectSocket();
};

export const initializeChatSocket = () => {
  const { initializeSocket } = useChatStore.getState();
  initializeSocket();
};

export default useChatStore;
