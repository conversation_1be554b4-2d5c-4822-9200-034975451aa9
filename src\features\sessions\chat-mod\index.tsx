import { HoldMessageModal } from "@/components/hold-message-modal";
import { ConfirmDialog } from "@/components/modal";
import { ProblemModal } from "@/components/problem-modal";
import { useParams } from "@tanstack/react-router";
import { useState, useEffect } from "react";
import { getConversationProfile } from "../api";
import CenterChatPanel from "./center-chat-panel";
import LeftPanel from "./left-panel";
import RightPanel from "./right-panel";
import useChatStore from "@/stores/useChatStore";
import { useAuthStore } from "@/stores/authStore";

// Interface for API message data
interface ApiMessageData {
  id: number;
  senderId: number;
  replyToId?: number | null;
  moderatorId?: number | null;
  conversationId: number;
  message: string;
  messageType?: string;
  messageStatus?: string;
  isRead?: boolean;
  metadata?: unknown;
  createdAt: string;
  updatedAt: string;
}

export default function ChatProfileView() {
  const { conversationId } = useParams({
    from: "/_authenticated/sessions/chat-mod/$conversationId",
  });
  const [show, setShow] = useState(false);
  const [showHold, setShowHold] = useState(false);
  const [showProblem, setShowProblem] = useState(false);

  const { initializeSocket, "mod-setMessages": modSetMessages } =
    useChatStore();
  const { auth } = useAuthStore();

  const currentUserId = auth.user?.accountNo
    ? parseInt(auth.user.accountNo, 10)
    : 0;

  const conversationIdNum = parseInt(conversationId, 10);

  const {
    data: {
      customer = {},
      model = {},
      messages: { messages = [], meta = {} } = {},
      problems = [],
    } = {},
  } = getConversationProfile(conversationId);

  useEffect(() => {
    const cleanup = initializeSocket();
    return cleanup;
  }, [initializeSocket]);

  useEffect(() => {
    if (messages && messages.length > 0 && conversationIdNum) {
      const moderatorMessages = messages.map((msg: ApiMessageData) => ({
        id: msg.id,
        senderId: msg.senderId,
        replyToId: msg.replyToId || null,
        moderatorId: msg.moderatorId || null,
        conversationId: msg.conversationId,
        message: msg.message,
        messageType: msg.messageType || "text",
        messageStatus: msg.messageStatus || "sent",
        isRead: msg.isRead || false,
        metadata: msg.metadata,
        createdAt: msg.createdAt,
        updatedAt: msg.updatedAt,
      }));

      modSetMessages(conversationIdNum, moderatorMessages, meta);
    }
  }, [messages, meta, conversationIdNum, modSetMessages]);

  return (
    <div className="grid grid-cols-1 xl:grid-cols-[1fr_2fr_1fr] h-screen w-full gap-[16px] px-3 py-4 sm:px-4 sm:py-6">
      <LeftPanel data={model} />

      <CenterChatPanel
        setShowHold={setShowHold}
        setShowProblem={setShowProblem}
        conversationId={conversationIdNum}
        currentUserId={currentUserId}
        modelInfo={model}
        customerInfo={customer}
      />

      <RightPanel data={customer} setShow={setShow} />

      <ProblemModal
        open={showProblem}
        onOpenChange={setShowProblem}
        isLoading={false}
        onSave={() => {}}
        problems={problems}
      />

      <HoldMessageModal
        open={showHold}
        onOpenChange={setShowHold}
        isLoading={false}
        onSave={() => {}}
      />

      <ConfirmDialog open={show} setOpen={setShow} data={customer} />
    </div>
  );
}
