import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ertDialog<PERSON><PERSON><PERSON>,
  AlertDialog<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useState } from "react";

interface Problem {
  id: string;
  title: string;
}
interface ProblemModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (selected: string | null) => void;
  isLoading?: boolean;
  problems: Problem[];
}

export function ProblemModal({
  open,
  onOpenChange,
  onSave,
  isLoading,
  problems,
}: ProblemModalProps) {
  const [selected, setSelected] = useState<string | null>(null);

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent width="max-w-xl" className="p-0 rounded-xl bg-card">
        <AlertDialogHeader className="px-6 pt-6 pb-2 text-left">
          <AlertDialogTitle className="text-2xl font-semibold">
            Problem
          </AlertDialogTitle>
        </AlertDialogHeader>
        <div className="px-6">
          <div className="grid grid-cols-3 gap-3 mb-2">
            {problems.map((problem) => (
              <button
                key={problem?.id}
                type="button"
                className={cn(
                  "border rounded-lg p-3 text-sm text-center transition-colors",
                  selected === problem?.id
                    ? "bg-primary text-primary-foreground border-primary"
                    : "bg-card text-card-foreground border-border hover:bg-accent hover:text-accent-foreground"
                )}
                onClick={() => setSelected(problem?.id)}
              >
                {problem?.title}
              </button>
            ))}
          </div>
        </div>
        <AlertDialogFooter className="px-6 pb-6 pt-2 flex-row-reverse gap-2">
          <AlertDialogCancel className="min-w-[120px] border-gray-300 bg-transparent cursor-pointer">
            Cancel
          </AlertDialogCancel>
          <Button
            onClick={() => onSave(selected)}
            disabled={isLoading || !selected}
            className="min-w-[120px] bg-[#171717] cursor-pointer"
          >
            Save
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
