import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "./api-endpoints";
import { apiClient } from "@/api/apiClient";


export const useGetModels = (params = {}) =>
    useQuery({
        queryFn: async () => {
            const response = await apiClient.get(API_ENDPOINTS.MODELS, {
                params,
            });
            return response?.data ?? {}; // return [] or {} as a fallback
        },
        queryKey: ["models-list"],
    });


export const addModelApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.post(API_ENDPOINTS.MODELS, payload);
        },
    });

export const updateModelApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.put(`${API_ENDPOINTS.MODELS}/${payload?.id}`, payload);
        },
    });

export const deleteModelApi = () =>
    useMutation({
        mutationFn: async (payload: any) => {
            return await apiClient.delete(`${API_ENDPOINTS.MODELS}/${payload?.id}`);
        },
    });

export const modelStatusChange = () =>
    useMutation({
        mutationFn: async (id: any) => {
            return await apiClient.get(`${API_ENDPOINTS.MODELS}/${id}${API_ENDPOINTS.MODELS_STATUS}`);
        },
    });


export const getModelDetails = (id: any = {}) =>
    useQuery({
        queryFn: async () => {
            if (typeof id === 'string') {
                const response = await apiClient.get(`${API_ENDPOINTS.MODELS}/${id}`)
                return response?.data ?? {}; // return [] or {} as a fallback
            }
            return {}

        },
        queryKey: ["model-details", id],
        enabled: !!id
    });

    export const getConversationProfile = (id: any = {}) =>
        useQuery({
            queryFn: async () => {
                if (typeof id === 'string') {
                    const response = await apiClient.get(`${API_ENDPOINTS.CONVERSATION}/${id}`)
                    return response?.data ?? {}; // return [] or {} as a fallback
                }
                return {}
    
            },
            queryKey: ["conversation-profile", id],
            enabled: !!id
        });

            